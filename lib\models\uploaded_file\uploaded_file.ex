defmodule Reconciliation.UploadedFile do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.{ReconciliationRun, Transaction}

  schema "uploaded_files" do
    field :file_type, :string
    field :filename, :string
    field :original_filename, :string
    field :file_size, :integer
    field :mime_type, :string
    field :file_path, :string
    field :status, :string, default: "uploaded"
    field :total_rows, :integer, default: 0
    field :processed_rows, :integer, default: 0
    field :error_rows, :integer, default: 0
    field :headers_detected, {:array, :string}
    field :column_mapping, :map
    field :processing_errors, {:array, :string}
    field :processed_at, :utc_datetime

    # File-provided summary data from CSV/Excel files
    field :file_total_transactions, :integer
    field :file_total_credits, :integer
    field :file_total_debits, :integer
    field :file_total_credit_amount, :decimal
    field :file_total_debit_amount, :decimal
    field :file_net_balance, :decimal
    field :file_total_amount, :decimal

    # Calculated summary data from processed transactions
    field :calculated_total_transactions, :integer
    field :calculated_total_credits, :integer
    field :calculated_total_debits, :integer
    field :calculated_total_credit_amount, :decimal
    field :calculated_total_debit_amount, :decimal
    field :calculated_net_balance, :decimal
    field :calculated_total_amount, :decimal

    # Validation flags
    field :summary_validation_passed, :boolean, default: false
    field :summary_validation_errors, {:array, :string}

    belongs_to :reconciliation_run, ReconciliationRun
    has_many :transactions, Transaction, on_delete: :delete_all

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :file_type, :filename, :original_filename, :file_size, :mime_type,
      :file_path, :status, :total_rows, :processed_rows, :error_rows,
      :headers_detected, :column_mapping, :processing_errors, :processed_at,
      :reconciliation_run_id,
      # Summary fields
      :file_total_transactions, :file_total_credits, :file_total_debits,
      :file_total_credit_amount, :file_total_debit_amount, :file_net_balance, :file_total_amount,
      :calculated_total_transactions, :calculated_total_credits, :calculated_total_debits,
      :calculated_total_credit_amount, :calculated_total_debit_amount, :calculated_net_balance, :calculated_total_amount,
      :summary_validation_passed, :summary_validation_errors
    ])
    |> validate_required([:file_type, :filename, :original_filename, :status, :reconciliation_run_id])
    |> validate_inclusion(:file_type, ["file_a", "file_b"])
    |> validate_inclusion(:status, ["uploaded", "processing", "processed", "failed"])
    |> validate_length(:filename, min: 1, max: 255)
    |> validate_length(:original_filename, min: 1, max: 255)
    |> validate_filename(:original_filename)
    |> validate_filename(:filename)
    |> validate_number(:file_size, greater_than: 0)
    |> validate_number(:total_rows, greater_than_or_equal_to: 0)
    |> validate_number(:processed_rows, greater_than_or_equal_to: 0)
    |> validate_number(:error_rows, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:reconciliation_run_id)
  end

  @doc """
  Creates a changeset for updating file processing status
  """
  def processing_changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :status, :total_rows, :processed_rows, :error_rows,
      :headers_detected, :column_mapping, :processing_errors, :processed_at,
      # Summary fields
      :file_total_transactions, :file_total_credits, :file_total_debits,
      :file_total_credit_amount, :file_total_debit_amount, :file_net_balance, :file_total_amount,
      :calculated_total_transactions, :calculated_total_credits, :calculated_total_debits,
      :calculated_total_credit_amount, :calculated_total_debit_amount, :calculated_net_balance, :calculated_total_amount,
      :summary_validation_passed, :summary_validation_errors
    ])
    |> validate_required([:status])
    |> validate_inclusion(:status, ["uploaded", "processing", "processed", "failed"])
    |> validate_number(:total_rows, greater_than_or_equal_to: 0)
    |> validate_number(:processed_rows, greater_than_or_equal_to: 0)
    |> validate_number(:error_rows, greater_than_or_equal_to: 0)
  end

  @doc """
  Creates a changeset for marking file processing as failed
  """
  def error_changeset(uploaded_file, errors) do
    uploaded_file
    |> cast(%{status: "failed", processing_errors: errors}, [:status, :processing_errors])
    |> validate_required([:status, :processing_errors])
  end

  @doc """
  Creates a changeset for updating summary data
  """
  def summary_changeset(uploaded_file, attrs) do
    uploaded_file
    |> cast(attrs, [
      :file_total_transactions, :file_total_credits, :file_total_debits,
      :file_total_credit_amount, :file_total_debit_amount, :file_net_balance, :file_total_amount,
      :calculated_total_transactions, :calculated_total_credits, :calculated_total_debits,
      :calculated_total_credit_amount, :calculated_total_debit_amount, :calculated_net_balance, :calculated_total_amount,
      :summary_validation_passed, :summary_validation_errors
    ])
    |> validate_number(:file_total_transactions, greater_than_or_equal_to: 0)
    |> validate_number(:calculated_total_transactions, greater_than_or_equal_to: 0)
  end

  @doc """
  Checks if the file has been processed successfully
  """
  def processed?(%__MODULE__{status: "processed"}), do: true
  def processed?(_), do: false

  @doc """
  Checks if the file is currently being processed
  """
  def processing?(%__MODULE__{status: "processing"}), do: true
  def processing?(_), do: false

  @doc """
  Checks if the file processing has failed
  """
  def failed?(%__MODULE__{status: "failed"}), do: true
  def failed?(_), do: false

  @doc """
  Calculates processing progress percentage
  """
  def progress_percentage(%__MODULE__{total_rows: total, processed_rows: processed})
      when total > 0 do
    (processed / total * 100) |> Float.round(1)
  end

  def progress_percentage(_), do: 0.0

  @doc """
  Returns the file extension from filename
  """
  def file_extension(%__MODULE__{filename: filename}) do
    filename
    |> Path.extname()
    |> String.downcase()
  end

  @doc """
  Checks if the file is an Excel file
  """
  def excel_file?(%__MODULE__{} = file) do
    file_extension(file) in [".xlsx", ".xls"]
  end

  @doc """
  Checks if the file is a CSV file
  """
  def csv_file?(%__MODULE__{} = file) do
    file_extension(file) == ".csv"
  end

  @doc """
  Returns human-readable file size
  """
  def human_file_size(%__MODULE__{file_size: nil}), do: "Unknown"
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024, do: "#{size} B"
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024 * 1024 do
    "#{Float.round(size / 1024, 1)} KB"
  end
  def human_file_size(%__MODULE__{file_size: size}) when size < 1024 * 1024 * 1024 do
    "#{Float.round(size / (1024 * 1024), 1)} MB"
  end
  def human_file_size(%__MODULE__{file_size: size}) do
    "#{Float.round(size / (1024 * 1024 * 1024), 1)} GB"
  end

  @doc """
  Returns the display name for the file, strongly preferring original filename
  This ensures users see their files with the exact names they uploaded
  """
  def display_name(%__MODULE__{original_filename: original}) when not is_nil(original) and original != "" do
    sanitize_filename_for_display(original)
  end

  def display_name(%__MODULE__{filename: filename}) when not is_nil(filename) do
    sanitize_filename_for_display(filename)
  end

  def display_name(_), do: "Unknown File"

  @doc """
  Returns a truncated display name for UI components with limited space
  """
  def short_display_name(%__MODULE__{} = file, max_length \\ 30) do
    name = display_name(file)

    # Remove file extension
    clean_name = Path.basename(name, Path.extname(name))

    # Replace underscores with spaces and clean up
    clean_name = clean_name
    |> String.replace("_", " ")
    |> String.replace("-", " ")
    |> String.split()
    |> Enum.map(&String.capitalize/1)
    |> Enum.join(" ")

    # Truncate if needed
    if String.length(clean_name) > max_length do
      String.slice(clean_name, 0, max_length - 3) <> "..."
    else
      clean_name
    end
  end

  @doc """
  Returns a clean display name without underscores and file extensions
  """
  def clean_display_name(%__MODULE__{} = file, max_length \\ 30) do
    name = display_name(file)

    # Remove file extension
    clean_name = Path.basename(name, Path.extname(name))

    # Replace underscores with spaces and clean up
    clean_name = clean_name
    |> String.replace("_", " ")
    |> String.replace("-", " ")
    |> String.split()
    |> Enum.map(&String.capitalize/1)
    |> Enum.join(" ")

    # Truncate if needed
    if String.length(clean_name) > max_length do
      String.slice(clean_name, 0, max_length - 3) <> "..."
    else
      clean_name
    end
  end

  @doc """
  Returns the original filename with enhanced display formatting
  Prioritizes showing the user's original filename exactly as uploaded
  """
  def original_display_name(%__MODULE__{original_filename: original} = file) when is_binary(original) do
    # Return the original filename as-is, but sanitized for display
    sanitize_filename_for_display(original)
  end

  def original_display_name(%__MODULE__{} = file) do
    # Fallback to regular display name if no original filename
    display_name(file)
  end

  @doc """
  Returns just the base name (without extension) of the original filename
  """
  def original_base_name(%__MODULE__{original_filename: original}) when is_binary(original) do
    original
    |> Path.basename(Path.extname(original))
    |> sanitize_filename_for_display()
  end

  def original_base_name(%__MODULE__{filename: filename}) when is_binary(filename) do
    filename
    |> Path.basename(Path.extname(filename))
    |> sanitize_filename_for_display()
  end

  def original_base_name(_), do: "file"

  @doc """
  Returns the file extension from the original filename
  """
  def original_extension(%__MODULE__{original_filename: original}) when is_binary(original) do
    Path.extname(original)
  end

  def original_extension(%__MODULE__{filename: filename}) when is_binary(filename) do
    Path.extname(filename)
  end

  def original_extension(_), do: ""

  @doc """
  Sanitizes filename for safe display in HTML while preserving readability
  """
  defp sanitize_filename_for_display(filename) when is_binary(filename) do
    filename
    |> String.replace(~r/[<>&"']/, "")  # Remove HTML-sensitive chars
    |> String.trim()
    |> case do
      "" -> "unnamed_file"
      clean -> clean
    end
  end

  defp sanitize_filename_for_display(_), do: "unnamed_file"

  @doc """
  Returns a very short identifier for the file (useful for compact displays)
  """
  def short_name(%__MODULE__{original_filename: original}) when not is_nil(original) and original != "" do
    base = Path.basename(original, Path.extname(original))
    ext = Path.extname(original)
    short_base = String.slice(base, 0, 8)
    "#{short_base}#{ext}"
  end

  def short_name(%__MODULE__{filename: filename}) when not is_nil(filename) do
    base = Path.basename(filename, Path.extname(filename))
    ext = Path.extname(filename)
    short_base = String.slice(base, 0, 8)
    "#{short_base}#{ext}"
  end

  def short_name(_), do: "file"

  # Legacy function - now delegates to the new function
  @doc """
  Sanitizes filename for safe display in HTML (legacy function)
  """
  defp sanitize_filename(filename), do: sanitize_filename_for_display(filename)

  @doc """
  Custom validation for filename fields
  """
  defp validate_filename(changeset, field) do
    validate_change(changeset, field, fn field, value ->
      cond do
        is_nil(value) or value == "" ->
          [{field, "cannot be blank"}]

        String.length(value) > 255 ->
          [{field, "is too long (maximum is 255 characters)"}]

        String.contains?(value, ["\0", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"]) ->
          [{field, "contains invalid characters"}]

        String.starts_with?(value, ".") and String.length(value) == 1 ->
          [{field, "cannot be just a dot"}]

        String.ends_with?(value, " ") or String.starts_with?(value, " ") ->
          [{field, "cannot start or end with spaces"}]

        true ->
          []
      end
    end)
  end
end
