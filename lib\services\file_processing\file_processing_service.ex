defmodule Reconciliation.Services.FileProcessingService do
  @moduledoc """
  Service for handling file upload and processing operations.
  Extracted from LiveView modules to follow proper separation of concerns.
  """

  require Logger
  alias Reconciliation.{UploadedFile, Repo}
  alias Reconciliation.Services.ExcelParser

  @doc """
  Process uploaded file with progress tracking
  """
  def process_file_with_progress(uploaded_file, reconciliation_run_id, progress_callback \\ nil) do
    try do
      # Validate file still exists
      unless File.exists?(uploaded_file.file_path) do
        raise "File no longer exists: #{uploaded_file.file_path}"
      end

      # Broadcast start of database insertion
      broadcast_progress(reconciliation_run_id, uploaded_file.id, %{
        status: "inserting",
        progress: 0,
        message: "Starting database insertion..."
      })

      # Update file status to processing
      case Reconciliation.update_uploaded_file(uploaded_file, %{status: "processing"}) do
        {:ok, _} -> :ok
        {:error, changeset} ->
          error_msg = format_changeset_errors(changeset)
          raise "Failed to update file status: #{error_msg}"
      end

      # Parse file with progress tracking
      case ExcelParser.parse_file_with_progress(uploaded_file, fn progress ->
        broadcast_progress(reconciliation_run_id, uploaded_file.id, progress)
        if progress_callback, do: progress_callback.(progress)
      end) do
        {:ok, result} ->
          # Broadcast completion
          broadcast_completion(reconciliation_run_id, uploaded_file.id, result)
          {:ok, result}

        {:error, error} ->
          # Mark file as failed and broadcast error
          Reconciliation.mark_file_failed(uploaded_file, [error])
          broadcast_error(reconciliation_run_id, uploaded_file.id, format_user_friendly_error(error))
          {:error, error}
      end

    rescue
      error ->
        # Mark file as failed
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        broadcast_error(reconciliation_run_id, uploaded_file.id, format_user_friendly_error(Exception.message(error)))
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Handle file upload and create database record
  """
  def handle_file_upload(%{path: temp_path} = _meta, entry, file_type, reconciliation_run_id) do
    try do
      # Define a permanent storage directory
      uploads_dir_base = Application.app_dir(:reconciliation, "priv/repo/uploads")
      run_specific_uploads_dir = Path.join(uploads_dir_base, "reconciliation_runs/#{reconciliation_run_id}")
      File.mkdir_p!(run_specific_uploads_dir)

      # Generate a unique filename for storage to avoid collisions
      unique_filename = generate_filename(entry.client_name)
      destination_path = Path.join(run_specific_uploads_dir, unique_filename)

      # Validate file exists at temporary path and is readable
      case File.stat(temp_path) do
        {:ok, %{size: size}} when size > 0 ->
          # Copy the file from the temporary path to the permanent destination
          File.cp!(temp_path, destination_path)
          Logger.info("[#{reconciliation_run_id}] Copied uploaded file from #{temp_path} to #{destination_path}")

          # Create uploaded file record with the new permanent path
          file_attrs = %{
            reconciliation_run_id: reconciliation_run_id,
            file_type: file_type,
            filename: unique_filename,
            original_filename: entry.client_name,
            file_size: size,
            mime_type: entry.client_type,
            file_path: destination_path,
            status: "uploaded"
          }

          case Reconciliation.create_uploaded_file(file_attrs) do
            {:ok, uploaded_file} ->
              {:ok, uploaded_file}
            {:error, changeset} ->
              error_msg = format_changeset_errors(changeset)
              {:error, "Failed to save file information: #{error_msg}"}
          end

        {:ok, %{size: 0}} ->
          {:error, "File is empty"}

        {:error, reason} ->
          {:error, "Cannot read file: #{inspect(reason)}"}
      end
    rescue
      error ->
        {:error, "Upload failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Generate unique filename that preserves original name while ensuring security and uniqueness
  """
  def generate_filename(original_name) do
    # Sanitize the original filename for security
    sanitized_name = sanitize_filename_for_storage(original_name)

    # Extract extension and base name
    extension = Path.extname(sanitized_name)
    base_name = Path.basename(sanitized_name, extension)

    # Generate a shorter, more readable unique identifier
    unique_id = generate_short_unique_id()

    # Create filename that preserves original structure: "original_name_ABC123.ext"
    "#{base_name}_#{unique_id}#{extension}"
  end

  @doc """
  Sanitize filename for safe storage while preserving readability
  """
  def sanitize_filename_for_storage(filename) when is_binary(filename) do
    filename
    # Remove or replace dangerous characters
    |> String.replace(~r/[<>:"\/\\|?*\x00-\x1f]/, "")  # Remove dangerous chars
    |> String.replace(~r/\.{2,}/, ".")                 # Replace multiple dots with single dot
    |> String.replace(~r/\s+/, "_")                    # Replace spaces with underscores
    |> String.replace(~r/_{2,}/, "_")                  # Replace multiple underscores with single
    |> String.trim(".")                                # Remove leading/trailing dots
    |> String.trim("_")                                # Remove leading/trailing underscores
    |> case do
      # Handle edge cases
      "" -> "file"
      "." -> "file"
      ".." -> "file"
      name when byte_size(name) > 200 ->
        # Preserve extension if filename is too long
        ext = Path.extname(name)
        base = Path.basename(name, ext)
        truncated_base = String.slice(base, 0, 200 - String.length(ext))
        "#{truncated_base}#{ext}"
      name -> name
    end
    |> ensure_valid_extension()
  end

  def sanitize_filename_for_storage(_), do: "file"

  @doc """
  Generate a short, readable unique identifier (6 characters)
  """
  def generate_short_unique_id do
    :crypto.strong_rand_bytes(3)
    |> Base.encode16(case: :upper)
  end

  # Ensure the filename has a valid extension, add .txt if missing
  defp ensure_valid_extension(filename) do
    case Path.extname(filename) do
      "" -> "#{filename}.txt"
      _ext -> filename
    end
  end

  # Private helper functions

  defp broadcast_progress(reconciliation_run_id, file_id, progress) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_progress, file_id, progress}
    )
  end

  defp broadcast_completion(reconciliation_run_id, file_id, result) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_complete, file_id, result}
    )
  end

  defp broadcast_error(reconciliation_run_id, file_id, error) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_error, file_id, error}
    )
  end

  defp format_user_friendly_error(error) when is_binary(error) do
    cond do
      String.contains?(error, "Unsupported file format") ->
        "This file format is not supported. Please upload an Excel (.xlsx, .xls) or CSV file."

      String.contains?(error, "Failed to parse") ->
        "Unable to read the file. Please check that the file is not corrupted and try again."

      String.contains?(error, "File is empty") ->
        "The uploaded file appears to be empty. Please check your file and try again."

      String.contains?(error, "Missing or invalid amount") ->
        "Some rows are missing required amount values. Please check your data and try again."

      String.contains?(error, "no data") ->
        "No transaction data found in the file. Please check that your file contains the expected data."

      true ->
        "An error occurred while processing your file. Please try again or contact support if the problem persists."
    end
  end
  defp format_user_friendly_error(error), do: "An unexpected error occurred: #{inspect(error)}"

  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end
end
