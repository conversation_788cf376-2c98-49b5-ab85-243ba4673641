defmodule Reconciliation.UploadedFileTest do
  use ExUnit.Case

  alias Reconciliation.UploadedFile

  describe "display_name/1" do
    test "returns original filename when available" do
      file = %UploadedFile{original_filename: "bank_statement.xlsx", filename: "bank_statement_123.xlsx"}
      assert UploadedFile.display_name(file) == "bank_statement.xlsx"
    end

    test "returns filename when original_filename is nil" do
      file = %UploadedFile{original_filename: nil, filename: "bank_statement_123.xlsx"}
      assert UploadedFile.display_name(file) == "bank_statement_123.xlsx"
    end

    test "returns 'Unknown File' when both are nil" do
      file = %UploadedFile{original_filename: nil, filename: nil}
      assert UploadedFile.display_name(file) == "Unknown File"
    end

    test "sanitizes HTML characters" do
      file = %UploadedFile{original_filename: "file<script>alert('xss')</script>.xlsx"}
      assert UploadedFile.display_name(file) == "filescriptalert('xss')/script.xlsx"
    end
  end

  describe "short_display_name/2" do
    test "truncates long filenames" do
      file = %UploadedFile{original_filename: "very_long_bank_statement_filename_that_exceeds_limit.xlsx"}
      result = UploadedFile.short_display_name(file, 20)
      assert String.length(result) <= 20
      assert String.ends_with?(result, "...")
    end

    test "keeps short filenames unchanged" do
      file = %UploadedFile{original_filename: "short.xlsx"}
      assert UploadedFile.short_display_name(file, 20) == "short.xlsx"
    end
  end

  describe "original_display_name/1" do
    test "returns original filename when available" do
      file = %UploadedFile{original_filename: "bank_statement.xlsx", filename: "bank_statement_123.xlsx"}
      assert UploadedFile.original_display_name(file) == "bank_statement.xlsx"
    end

    test "sanitizes HTML characters in original filename" do
      file = %UploadedFile{original_filename: "file<script>alert('xss')</script>.xlsx"}
      assert UploadedFile.original_display_name(file) == "filescriptalert('xss')/script.xlsx"
    end

    test "falls back to filename when original_filename is nil" do
      file = %UploadedFile{original_filename: nil, filename: "bank_statement_123.xlsx"}
      assert UploadedFile.original_display_name(file) == "bank_statement_123.xlsx"
    end

    test "handles malicious filenames safely" do
      malicious_names = [
        "../../etc/passwd",
        "file<>&\"'.xlsx",
        "file\x00null.xlsx",
        "file\r\ninjection.xlsx"
      ]

      for malicious_name <- malicious_names do
        file = %UploadedFile{original_filename: malicious_name}
        result = UploadedFile.original_display_name(file)

        # Should not contain dangerous characters
        refute String.contains?(result, ["<", ">", "&", "\"", "'"])
        refute String.contains?(result, ["\x00", "\r", "\n"])
        assert is_binary(result)
        assert String.length(result) > 0
      end
    end
  end

  describe "original_base_name/1" do
    test "returns base name without extension" do
      file = %UploadedFile{original_filename: "bank_statement.xlsx"}
      assert UploadedFile.original_base_name(file) == "bank_statement"
    end

    test "handles files without extensions" do
      file = %UploadedFile{original_filename: "bank_statement"}
      assert UploadedFile.original_base_name(file) == "bank_statement"
    end

    test "sanitizes base name" do
      file = %UploadedFile{original_filename: "file<script>.xlsx"}
      assert UploadedFile.original_base_name(file) == "filescript"
    end
  end

  describe "original_extension/1" do
    test "returns extension from original filename" do
      file = %UploadedFile{original_filename: "bank_statement.xlsx"}
      assert UploadedFile.original_extension(file) == ".xlsx"
    end

    test "returns empty string for files without extension" do
      file = %UploadedFile{original_filename: "bank_statement"}
      assert UploadedFile.original_extension(file) == ""
    end

    test "falls back to filename extension when original is nil" do
      file = %UploadedFile{original_filename: nil, filename: "bank_statement.csv"}
      assert UploadedFile.original_extension(file) == ".csv"
    end
  end

  describe "short_name/1" do
    test "creates short identifier from original filename" do
      file = %UploadedFile{original_filename: "bank_statement_january.xlsx"}
      assert UploadedFile.short_name(file) == "bank_sta.xlsx"
    end

    test "handles files without extension" do
      file = %UploadedFile{original_filename: "bank_statement"}
      assert UploadedFile.short_name(file) == "bank_sta"
    end
  end


end
