defmodule ReconciliationWeb.SummaryLiveTest do
  use ReconciliationWeb.ConnCase

  import Phoenix.LiveViewTest
  import Reconciliation.AccountsFixtures
  import Reconciliation.ReconciliationFixtures

  describe "Summary page" do
    setup :register_and_log_in_user

    test "displays empty state when no reconciliation data exists", %{conn: conn, user: user} do
      {:ok, _view, html} = live(conn, ~p"/summary")
      
      assert html =~ "No Reconciliation Data"
      assert html =~ "You haven't created any reconciliation runs yet"
      assert html =~ "Create Your First Reconciliation"
    end

    test "displays summary statistics when reconciliation data exists", %{conn: conn, user: user} do
      # Create test reconciliation run with completed status
      run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        total_transactions_a: 100,
        total_transactions_b: 95,
        matched_count: 90,
        unmatched_a_count: 10,
        unmatched_b_count: 5,
        match_rate: Decimal.new("90.0")
      })

      # Create uploaded files
      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_a",
        filename: "bank_statements.csv",
        file_size: 1024
      })

      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_b",
        filename: "transactions.csv",
        file_size: 2048
      })

      {:ok, _view, html} = live(conn, ~p"/summary")
      
      # Check that key metrics are displayed
      assert html =~ "Files Uploaded"
      assert html =~ "Total Transactions"
      assert html =~ "Overall Match Rate"
      assert html =~ "Completed Runs"
      
      # Check specific values
      assert html =~ "2" # Total files
      assert html =~ "195" # Total transactions (100 + 95)
      assert html =~ "90.0%" # Match rate
      assert html =~ "1" # Completed runs
    end

    test "allows filtering by time period", %{conn: conn, user: user} do
      # Create an older reconciliation run
      old_run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        inserted_at: DateTime.add(DateTime.utc_now(), -60, :day)
      })

      # Create a recent reconciliation run
      recent_run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        inserted_at: DateTime.utc_now()
      })

      {:ok, view, _html} = live(conn, ~p"/summary")
      
      # Filter to last 30 days
      html = 
        view
        |> form("select", %{period: "last_30_days"})
        |> render_change()
      
      # Should only show recent data
      assert html =~ "Last 30 Days"
    end

    test "handles refresh data event", %{conn: conn, user: user} do
      run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed"
      })

      {:ok, view, _html} = live(conn, ~p"/summary")
      
      # Trigger refresh
      html = render_click(view, "refresh_data")
      
      assert html =~ "Summary data refreshed successfully!"
    end

    test "displays file upload statistics correctly", %{conn: conn, user: user} do
      run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed"
      })

      # Create files with different types and sizes
      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_a",
        file_size: 1024
      })

      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_b",
        file_size: 2048
      })

      {:ok, _view, html} = live(conn, ~p"/summary")
      
      assert html =~ "File Upload Statistics"
      assert html =~ "Total Files"
      assert html =~ "Total Size"
      assert html =~ "File Type Breakdown"
      assert html =~ "Bank Statements"
      assert html =~ "Transaction Files"
    end

    test "displays transaction matching metrics correctly", %{conn: conn, user: user} do
      run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        total_transactions_a: 50,
        total_transactions_b: 45,
        matched_count: 40,
        unmatched_a_count: 10,
        unmatched_b_count: 5,
        match_rate: Decimal.new("80.0")
      })

      {:ok, _view, html} = live(conn, ~p"/summary")

      assert html =~ "Transaction Matching Metrics"
      assert html =~ "Total Processed A"
      assert html =~ "Total Processed B"
      assert html =~ "Total Processed"
      assert html =~ "Successfully Matched"
      assert html =~ "Unmatched"
      assert html =~ "50" # Total processed A
      assert html =~ "45" # Total processed B
      assert html =~ "40" # Matched
      assert html =~ "10" # Unmatched (50 - 40)
      assert html =~ "80.0%" # Match rate (40/50 * 100)
    end

    test "displays status overview correctly", %{conn: conn, user: user} do
      # Create runs with different statuses
      reconciliation_run_fixture(%{user_id: user.id, status: "completed"})
      reconciliation_run_fixture(%{user_id: user.id, status: "pending"})
      reconciliation_run_fixture(%{user_id: user.id, status: "failed"})

      {:ok, _view, html} = live(conn, ~p"/summary")
      
      assert html =~ "Status Overview"
      assert html =~ "Completed"
      assert html =~ "Pending"
      assert html =~ "Failed"
      assert html =~ "Success Rate"
    end

    test "displays quick actions section", %{conn: conn, user: user} do
      {:ok, _view, html} = live(conn, ~p"/summary")
      
      assert html =~ "Quick Actions"
      assert html =~ "New Reconciliation"
      assert html =~ "View Reports"
      assert html =~ "View Transactions"
    end

    test "calculates match rate correctly using max transactions logic", %{conn: conn, user: user} do
      # Create a run where file A has more transactions than file B
      run1 = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        total_transactions_a: 100,  # File A has 100 transactions
        total_transactions_b: 80,   # File B has 80 transactions
        matched_count: 75,          # 75 matches were found
        match_rate: Decimal.new("75.0")
      })

      # Create another run where file B has more transactions than file A
      run2 = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        total_transactions_a: 60,   # File A has 60 transactions
        total_transactions_b: 90,   # File B has 90 transactions
        matched_count: 50,          # 50 matches were found
        match_rate: Decimal.new("55.56")
      })

      {:ok, _view, html} = live(conn, ~p"/summary")

      # Expected calculation:
      # Run 1: max(100, 80) = 100 possible matches, 75 actual matches
      # Run 2: max(60, 90) = 90 possible matches, 50 actual matches
      # Total: 190 possible matches, 125 actual matches
      # Match rate: 125/190 * 100 = 65.79%

      assert html =~ "190" # Total processed (100 + 90)
      assert html =~ "125" # Total matched (75 + 50)
      assert html =~ "65" # Unmatched (190 - 125)
      assert html =~ "65.79%" # Match rate
    end

    test "navigation links work correctly", %{conn: conn, user: user} do
      {:ok, view, _html} = live(conn, ~p"/summary")

      # Test navigation to reconciliation page
      assert {:error, {:live_redirect, %{to: "/reconciliation"}}} =
        render_click(view, "phx-click", %{"href" => "/reconciliation"})
    end

    test "displays actual file names in table headers when specific run is selected", %{conn: conn, user: user} do
      # Create test reconciliation run
      run = reconciliation_run_fixture(%{
        user_id: user.id,
        status: "completed",
        total_transactions_a: 50,
        total_transactions_b: 45,
        matched_count: 40
      })

      # Create uploaded files with specific names
      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_a",
        filename: "bank_statements.csv",
        original_filename: "bank_statements.csv",
        file_size: 1024
      })

      uploaded_file_fixture(%{
        reconciliation_run_id: run.id,
        file_type: "file_b",
        filename: "transactions.csv",
        original_filename: "transactions.csv",
        file_size: 2048
      })

      {:ok, view, html} = live(conn, ~p"/summary")

      # Initially should show generic "File A" and "File B" labels when no specific run is selected
      assert html =~ "File A Credit"
      assert html =~ "File B Credit"
      assert html =~ "File A Debit"
      assert html =~ "File B Debit"

      # Filter by the specific run
      html =
        view
        |> element("select[name='run_id']")
        |> render_change(%{run_id: run.id})

      # Now should show actual file names (short display names)
      assert html =~ "Bank Statements Credit"
      assert html =~ "Transactions Credit"
      assert html =~ "Bank Statements Debit"
      assert html =~ "Transactions Debit"

      # Should not show generic labels anymore
      refute html =~ "File A Credit"
      refute html =~ "File B Credit"
      refute html =~ "File A Debit"
      refute html =~ "File B Debit"
    end
  end
end
