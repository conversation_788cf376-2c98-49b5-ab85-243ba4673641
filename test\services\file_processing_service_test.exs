defmodule Reconciliation.Services.FileProcessingServiceTest do
  use ExUnit.Case

  alias Reconciliation.Services.FileProcessingService

  describe "generate_filename/1" do
    test "preserves original filename structure with unique identifier" do
      original = "bank_statement.xlsx"
      result = FileProcessingService.generate_filename(original)
      
      # Should contain the base name
      assert String.contains?(result, "bank_statement")
      # Should have the same extension
      assert String.ends_with?(result, ".xlsx")
      # Should have a unique identifier
      assert String.match?(result, ~r/bank_statement_[A-F0-9]{6}\.xlsx/)
    end

    test "handles filenames with spaces" do
      original = "my bank statement.xlsx"
      result = FileProcessingService.generate_filename(original)
      
      # Spaces should be converted to underscores
      assert String.contains?(result, "my_bank_statement")
      assert String.ends_with?(result, ".xlsx")
    end

    test "handles filenames without extensions" do
      original = "document"
      result = FileProcessingService.generate_filename(original)
      
      # Should add .txt extension
      assert String.ends_with?(result, ".txt")
      assert String.contains?(result, "document")
    end

    test "generates unique identifiers for same filename" do
      original = "test.xlsx"
      result1 = FileProcessingService.generate_filename(original)
      result2 = FileProcessingService.generate_filename(original)
      
      # Should be different due to unique identifiers
      refute result1 == result2
      # But both should contain the base name
      assert String.contains?(result1, "test")
      assert String.contains?(result2, "test")
    end
  end

  describe "sanitize_filename_for_storage/1" do
    test "removes dangerous characters" do
      dangerous = "file<>:\"/\\|?*.xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(dangerous)
      
      # Should not contain any dangerous characters
      refute String.contains?(result, ["<", ">", ":", "\"", "/", "\\", "|", "?", "*"])
      assert String.ends_with?(result, ".xlsx")
    end

    test "replaces spaces with underscores" do
      spaced = "my bank statement.xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(spaced)
      
      assert result == "my_bank_statement.xlsx"
    end

    test "handles multiple consecutive spaces and underscores" do
      messy = "file   with    spaces___and___underscores.xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(messy)
      
      # Should normalize to single underscores
      assert result == "file_with_spaces_and_underscores.xlsx"
    end

    test "removes leading and trailing dots and underscores" do
      messy = "...___file___.xlsx..."
      result = FileProcessingService.sanitize_filename_for_storage(messy)
      
      assert result == "file.xlsx"
    end

    test "handles very long filenames" do
      long_name = String.duplicate("a", 250) <> ".xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(long_name)
      
      # Should be truncated but preserve extension
      assert String.length(result) <= 205  # 200 + ".xlsx"
      assert String.ends_with?(result, ".xlsx")
    end

    test "handles edge cases" do
      edge_cases = [
        "",
        ".",
        "..",
        "...",
        "___",
        "   ",
        nil
      ]

      for edge_case <- edge_cases do
        result = FileProcessingService.sanitize_filename_for_storage(edge_case)
        assert result == "file.txt"
      end
    end

    test "preserves valid extensions" do
      files = [
        "document.pdf",
        "spreadsheet.xlsx",
        "data.csv",
        "image.png"
      ]

      for file <- files do
        result = FileProcessingService.sanitize_filename_for_storage(file)
        assert String.ends_with?(result, Path.extname(file))
      end
    end

    test "adds .txt extension when missing" do
      no_ext = "document"
      result = FileProcessingService.sanitize_filename_for_storage(no_ext)
      
      assert result == "document.txt"
    end

    test "handles null bytes and control characters" do
      malicious = "file\x00\x01\x02\x1f.xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(malicious)
      
      # Should remove all control characters
      assert result == "file.xlsx"
    end

    test "handles multiple dots correctly" do
      dotty = "file...with...dots.xlsx"
      result = FileProcessingService.sanitize_filename_for_storage(dotty)
      
      assert result == "file.with.dots.xlsx"
    end
  end

  describe "generate_short_unique_id/0" do
    test "generates 6-character uppercase hex string" do
      id = FileProcessingService.generate_short_unique_id()
      
      assert String.length(id) == 6
      assert String.match?(id, ~r/^[A-F0-9]{6}$/)
    end

    test "generates unique identifiers" do
      id1 = FileProcessingService.generate_short_unique_id()
      id2 = FileProcessingService.generate_short_unique_id()
      
      refute id1 == id2
    end

    test "generates multiple unique identifiers" do
      ids = for _ <- 1..100, do: FileProcessingService.generate_short_unique_id()
      unique_ids = Enum.uniq(ids)
      
      # Should have 100 unique identifiers
      assert length(unique_ids) == 100
    end
  end

  describe "filename security tests" do
    test "prevents directory traversal attacks" do
      attacks = [
        "../../../etc/passwd",
        "..\\..\\windows\\system32\\config\\sam",
        "file/../../secret.txt",
        "file\\..\\..\\secret.txt"
      ]

      for attack <- attacks do
        result = FileProcessingService.sanitize_filename_for_storage(attack)
        
        # Should not contain path separators
        refute String.contains?(result, ["/", "\\"])
        refute String.contains?(result, "..")
      end
    end

    test "prevents reserved filename attacks" do
      # Windows reserved names
      reserved = ["CON", "PRN", "AUX", "NUL", "COM1", "LPT1"]
      
      for name <- reserved do
        result = FileProcessingService.sanitize_filename_for_storage(name)
        # Should be safe (though we don't specifically handle reserved names,
        # the .txt extension makes them safe)
        assert String.ends_with?(result, ".txt")
      end
    end

    test "handles unicode and international characters safely" do
      unicode_names = [
        "файл.xlsx",  # Cyrillic
        "文件.xlsx",   # Chinese
        "ファイル.xlsx", # Japanese
        "αρχείο.xlsx"  # Greek
      ]

      for name <- unicode_names do
        result = FileProcessingService.sanitize_filename_for_storage(name)
        
        # Should preserve unicode characters (they're not dangerous)
        assert String.ends_with?(result, ".xlsx")
        assert String.length(result) > 0
      end
    end
  end
end
