defmodule Reconciliation.ReconciliationFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `Reconciliation` context.
  """

  alias Reconciliation.Reconciliation

  def reconciliation_run_fixture(attrs \\ %{}) do
    default_attrs = %{
      name: "Test Reconciliation Run",
      description: "Test run for testing",
      status: "pending",
      total_transactions_a: 0,
      total_transactions_b: 0,
      matched_count: 0,
      unmatched_a_count: 0,
      unmatched_b_count: 0,
      total_amount_a: Decimal.new("0"),
      total_amount_b: Decimal.new("0"),
      difference_amount: Decimal.new("0"),
      match_rate: Decimal.new("0")
    }

    attrs = Enum.into(attrs, default_attrs)
    
    {:ok, run} = Reconciliation.create_reconciliation_run(attrs)
    run
  end

  def uploaded_file_fixture(attrs \\ %{}) do
    default_attrs = %{
      file_type: "file_a",
      filename: "test_file.csv",
      original_filename: "test_file.csv",
      file_size: 1024,
      mime_type: "text/csv",
      file_path: "/tmp/test_file.csv",
      status: "uploaded",
      total_rows: 0,
      processed_rows: 0,
      error_rows: 0,
      headers_detected: ["Date", "Amount", "Description"],
      column_mapping: %{},
      processing_errors: []
    }

    attrs = Enum.into(attrs, default_attrs)
    
    {:ok, file} = Reconciliation.create_uploaded_file(attrs)
    file
  end

  def transaction_fixture(attrs \\ %{}) do
    default_attrs = %{
      row_number: 1,
      transaction_date: ~D[2024-01-15],
      transaction_id: "TXN001",
      reference: "REF001",
      description: "Test Transaction",
      amount: Decimal.new("100.00"),
      transaction_type: "debit",
      account: "ACC001",
      category: "Test",
      currency: "USD",
      raw_data: %{},
      is_matched: false,
      match_confidence: nil,
      validation_errors: []
    }

    attrs = Enum.into(attrs, default_attrs)
    
    {:ok, transaction} = Reconciliation.create_transaction(attrs)
    transaction
  end
end
